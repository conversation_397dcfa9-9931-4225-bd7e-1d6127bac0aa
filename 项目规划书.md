好的，遵照您的指示。这是一份为桌面应用版 "Chronicler" 编写的、独立且完整的项目规划书，其中未提及任何历史版本信息。

-----

### **项目规划书：AI小说自动化生成系统 (Project "Chronicler")**

#### **1. 项目愿景 (Project Vision)**

创建一个以作者为中心、高度自动化的写作辅助系统。通过直观的图形化桌面应用，将小说创作过程结构化，利用大型语言模型（LLM）处理重复性、程式化的写作任务。最终目标是让各种水平的作者都能轻松上手，将精力从繁琐的状态管理中解放出来，专注于故事的核心创意、情感表达和艺术打磨，实现人机协作下的高质量、高效率内容生产。

#### **2. 核心原则 (Core Principle)**

**约定优于配置 (Convention over Configuration):** 这是项目的基石。系统的所有状态和数据**唯一、真实地**存储在用户硬盘的文件系统中。桌面应用本身不存储任何数据，它仅仅是这个文件系统结构的一个**可视化操作界面**。用户的每一次点击、保存，都直接转化为对项目文件夹内文件的读写操作。这保证了项目的透明性、持久性和可移植性。

#### **3. 系统架构 (System Architecture)**

##### **3.1. 项目目录结构 (The "State Database")**

文件系统是后台的“数据库”，GUI是它的“前端”。

```
/my_epic_novel/
|
├──  chronicler.yml         # 主配置文件 (API密钥, 项目名, 作者等)
|
├── 1_knowledge_base/       # 知识库: 宇宙的真理
|   ├── characters/
|   |   ├── john_doe.yml
|   |   └── jane_smith.yml
|   ├── locations/
|   |   └── blackwood_tavern.yml
|   └── world_bible.md      # 世界观设定、历史、物理法则等
|
├── 2_plot/                 # 情节蓝图: 创作者的意图
|   ├── main_outline.md     # 主线大纲
|   └── scene_cards/
|       ├── sc_001_introduction.md
|       └── ...
|
├── 3_manuscript/           # 手稿: AI与人的创作成果
|   ├── drafts/             # AI生成的初稿 (待审阅)
|   |   └── sc_001_introduction.md
|   └── published/          # 用户审阅修订后的最终稿
|       ├── ch_001.md
|       └── ...
|
└── 4_summaries/            # 机器可读的摘要库 (驱动状态更新)
    ├── sc_001_introduction.yml
    └── ...
```

##### **3.2. 核心数据模式 (Core Data Schemas)**

**A. 角色文件 (`characters/john_doe.yml`)**
此文件是角色的“活档案”，包含了其静态属性和由系统自动更新的动态状态。

```yaml
name: John Doe
archetype: The Reluctant Hero
motivation: To find a cure for his sister's illness.
appearance: Tall, with a scar over his left eye.

# 角色所知信息（可能不完整或错误）
knowledge:
  - The layout of his home village.
  - Basic herbal remedies.
beliefs: # 角色认为是“事实”的信念，不一定是真相
  - "The mayor is a trustworthy person."
secrets: # 角色知道但隐藏的信息
  - "Stole an apple from the market last week."

# 角色的动态状态，会被系统自动更新
state:
  emotion: Anxious
  location: blackwood_tavern # 使用地点的ID进行关联
  inventory:
    - Rusty Sword
    - 3 Gold Coins

# 角色关系，会被系统自动更新
relationships:
  jane_smith:
    description: "Childhood friend."
    status: Trusting # 可选值: Trusting, Strained, Romantic, Hostile...
    knowledge_level: 8 # (0-10, 对彼此的了解程度)
```

**B. 场景卡片 (`scene_cards/sc_001_introduction.md`)**
这是给AI下达的核心指令，包含了结构化的目标和简要描述。

```yaml
---
scene_id: sc_001_introduction
storyline: main # 用于区分主线(main)、支线(side_quest_A)等
characters:
  - john_doe
  - jane_smith
location: blackwood_tavern
goal: # 结构化的场景目标，指导AI生成内容
  - type: exposition
    content: "Introduce John's motivation (sick sister) through a dialogue with Jane."
  - type: character_interaction
    content: "Show the deep trust between John and Jane, solidifying their 'Trusting' relationship status."
  - type: plot_hook
    content: "End the scene with a mysterious stranger entering the tavern, creating suspense."
---

# 场景简述
约翰在酒馆里和简聊天，神情忧虑。他提到为了给妹妹治病，他愿意做任何事。简安慰他。场景结束时，一个神秘的陌生人推门而入。
```

**C. 机器可读摘要 (`summaries/sc_001_introduction.yml`)**
这是应用在“提交”环节的核心产物，用于精确地更新系统状态。

```yaml
# 此文件由AI生成，供机器读取，以确保状态更新的准确性
scene_id: sc_001_introduction
next_scene_recommendation: "John should investigate the stranger or the note he received."

# 核心状态变更
state_updates:
  john_doe:
    emotion: Hopeful # 情感从之前的 Anxious 变为 Hopeful
    inventory_add: [Mysterious Note]

# 核心关系变更
relationship_updates:
  john_doe:
    mysterious_stranger: # 新增关系
      description: "A stranger who offered a potential cure in the tavern."
      status: Suspicious
      knowledge_level: 1

# 新揭露的关键信息
new_information_revealed:
  - "A potential cure for John's sister might exist."
  - "A mysterious stranger is aware of John's predicament."
```

#### **4. 核心功能与图形化工作流 (GUI Workflow)**

应用的所有功能都通过直观的图形界面操作完成。

**1. 初始化项目**

  * **界面交互:** 用户点击菜单栏的“文件”-\>“新建项目”。弹出一个对话框，要求用户输入“项目名称”并选择一个本地文件夹作为项目根目录。
  * **后台逻辑:** 点击“创建”后，应用在该位置自动创建完整的项目目录结构和模板文件。完成后，应用主界面刷新，显示新项目的概览。

**2. 规划新场景**

  * **界面交互:** 在界面的“情节规划”或“场景卡片”区域，有一个醒目的“+ 新建场景”按钮。点击后，弹出一个表单窗口。表单内包含输入框（`场景ID`）、下拉菜单（`故事线`、`角色`、`地点`，选项自动从`knowledge_base`读取）和一个用于添加/编辑结构化`目标`的动态列表。
  * **后台逻辑:** 用户填写完毕点击“保存”，应用将在 `2_plot/scene_cards/` 目录下生成对应的 `.md` 文件。

**3. 生成初稿**

  * **界面交互:** 在“场景卡片”列表中，每个未生成的场景旁边都有一个“生成初稿”按钮。用户点击该按钮。
  * **后台逻辑:** 应用后台执行完整的“上下文汇编”和“API调用”流程。界面上会显示加载动画或进度提示（如“正在与AI沟通...”）。任务完成后，生成的稿件会出现在界面的“草稿箱”区域，并带上“待审阅”标记。

**4. 提交终稿**

  * **界面交互:** 用户在“草稿箱”中点击一篇稿件，应用内置的文本编辑器会加载其内容。用户进行审阅、修改、润色。完成创作后，点击编辑器上方或侧边的“完成并提交”按钮。
  * **后台逻辑:** 这是最关键的自动化步骤。
    1.  应用弹出确认对话框。
    2.  确认后，应用后台读取编辑器中的最终文本，调用AI进行分析，生成机器可读的YAML摘要。
    3.  将摘要存入 `4_summaries/` 目录。
    4.  **根据摘要内容，自动更新 `1_knowledge_base/characters/` 下的角色文件。**
    5.  将稿件从 `3_manuscript/drafts/` 移动到 `published/` 目录。
    6.  **界面刷新：** 该场景从“草稿箱”列表中消失，出现在“已发布”列表中；同时，“角色”面板中对应角色的状态（如情绪、物品、关系）会**实时更新**，即时反馈“提交”操作带来的世界变化。

#### **5. 标准工作流演示 (GUI Workflow Demonstration)**

1.  **初始化:** 作者打开Chronicler应用，点击“新建项目”，命名为 `my_first_novel` 并选择保存位置。
2.  **世界构建:** 在左侧导航栏点击“知识库”，应用以表单或列表形式展示角色和地点。作者点击“新建角色”，在表单中填写 `john_doe` 的信息并保存，应用自动创建 `john_doe.yml` 文件。
3.  **情节规划:** 作者切换到“情节”视图，点击“新建场景”，在弹出的表单中填写场景信息，并为 `goal` 添加了几条指令。保存后，`sc_001` 出现在“待办场景”列表中。
4.  **AI生成:** 作者点击 `sc_001` 旁边的“生成初稿”按钮。几秒钟后，系统提示生成完毕，`sc_001` 出现在“草稿箱”中。
5.  **作者创作:** **【核心创作环节】** 作者点击打开 `sc_001` 草稿，在应用内置的富文本编辑器中进行修改和润色，直到满意。
6.  **系统提交:** 作者点击“完成并提交”按钮。
      * **瞬间发生的变化：** `sc_001` 从“草稿箱”移至“已发布”列表。作者切换到“知识库”视图查看 `john_doe`，发现他的情绪已从“焦虑”变为“充满希望”，并且关系列表中增加了一个与“神秘人”的“可疑”关系。整个世界的状态因为这次提交而前进了。
7.  **循环:** 作者继续规划下一个场景 `sc_002`。当再次生成时，系统将读取到约翰的最新状态，确保故事的连续性和角色成长。

#### **6. 技术栈建议 (Recommended Tech Stack)**

  * **应用框架:** **Electron**
  * **前端界面:**
      * **核心:** HTML, CSS, TypeScript/JavaScript
      * **UI框架 (推荐):** **Vue.js**
  * **后台逻辑 (Node.js):**
      * **文件系统:** Node.js 内置的 `fs` 模块
      * **数据文件处理:** `js-yaml` (用于读写YAML), `front-matter` (用于处理Markdown的Frontmatter)
      * **Prompt模板引擎:** **Nunjucks** (类似Python的Jinja2) 或功能更强的模板库
  * **AI模型接口:** `openai` 的官方NPM包
  * **应用打包与分发:** **Electron Builder**